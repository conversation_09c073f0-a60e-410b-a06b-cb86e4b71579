// Products Page Functionality - Updated for Dynamic System
function filterProducts(category) {
    // Map old category names to new ones
    const categoryMap = {
        'all': 'all-products',
        'detergent-powder': 'detergent-powder',
        'dishwashing': 'dishwashing',
        'floor-cleaner': 'floor-cleaner',
        'detergent-liquid': 'detergent-liquid',
        'glass-cleaner': 'glass-cleaner',
        'hand-washing': 'hand-washing',
        'bleach': 'bleach',
        'toilet-cleaner': 'toilet-cleaner'
    };

    const mappedCategory = categoryMap[category] || category;

    // Use the new showCategory function if it exists
    if (typeof window.showCategory === 'function') {
        window.showCategory(mappedCategory);
        return;
    }

    // Fallback to old method
    // Update active sidebar category
    const sidebarCategories = document.querySelectorAll('.category-item, .sidebar-category');
    sidebarCategories.forEach(cat => cat.classList.remove('active'));

    // Find and activate the clicked category
    const activeCategory = document.querySelector(`[data-category="${mappedCategory}"]`);
    if (activeCategory) {
        activeCategory.classList.add('active');
    }

    // Show/hide product sections
    const productSections = document.querySelectorAll('.product-section');
    let visibleCount = 0;

    productSections.forEach(section => {
        if (mappedCategory === 'all-products') {
            section.classList.remove('active');
            if (section.id === 'all-products') {
                section.classList.add('active');
                const products = section.querySelectorAll('.product-card');
                visibleCount += products.length;
            }
        } else {
            section.classList.remove('active');
            if (section.id === mappedCategory) {
                section.classList.add('active');
                const products = section.querySelectorAll('.product-card');
                visibleCount += products.length;
            }
        }
    });

    // Update product count
    const productCountElement = document.getElementById('product-count');
    if (productCountElement) {
        productCountElement.textContent = visibleCount;
    }
}

// View toggle functionality
function setView(viewType) {
    const viewButtons = document.querySelectorAll('.view-btn');
    const productsGrid = document.querySelector('.products-grid');
    
    // Update active button
    viewButtons.forEach(btn => btn.classList.remove('active'));
    const activeButton = document.querySelector(`[onclick="setView('${viewType}')"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }
    
    // Apply view styles
    if (viewType === 'list') {
        productsGrid.style.gridTemplateColumns = '1fr';
        productsGrid.style.gap = '1rem';
    } else {
        productsGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(400px, 1fr))';
        productsGrid.style.gap = '2rem';
    }
}

// Product image switching
function switchProductImage(productCard, imageElement) {
    const images = productCard.querySelectorAll('.product-image');
    images.forEach(img => img.classList.remove('active'));
    imageElement.classList.add('active');
}

// Initialize product page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers to product images
    const productImages = document.querySelectorAll('.product-image');
    productImages.forEach(image => {
        image.addEventListener('click', function() {
            const productCard = this.closest('.product-card');
            switchProductImage(productCard, this);
        });
    });

    // Initialize intersection observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Observe all product sections
    const productSections = document.querySelectorAll('.product-section');
    productSections.forEach(section => {
        observer.observe(section);
    });

    // Handle URL hash for direct category linking
    const hash = window.location.hash.substring(1);
    if (hash) {
        setTimeout(() => {
            filterProducts(hash);
        }, 100);
    }
});

// Price item hover effects
document.addEventListener('DOMContentLoaded', function() {
    const priceItems = document.querySelectorAll('.price-item');
    priceItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});

