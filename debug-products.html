<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Products Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-panel { position: fixed; top: 10px; right: 10px; width: 300px; background: #f8f9fa; border: 1px solid #ccc; padding: 10px; max-height: 80vh; overflow-y: auto; z-index: 9999; }
        .debug-log { font-size: 12px; margin: 5px 0; padding: 5px; background: #fff; border-left: 3px solid #007bff; }
        .debug-error { border-left-color: #dc3545; background: #f8d7da; }
        .debug-success { border-left-color: #28a745; background: #d4edda; }
        .debug-warning { border-left-color: #ffc107; background: #fff3cd; }
        .clear-btn { background: #dc3545; color: white; border: none; padding: 5px 10px; cursor: pointer; margin-bottom: 10px; }
        .test-btn { background: #007bff; color: white; border: none; padding: 5px 10px; cursor: pointer; margin: 2px; }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h3>Debug Console</h3>
        <button class="clear-btn" onclick="clearDebugLog()">Clear Log</button>
        <button class="test-btn" onclick="testFunctions()">Test Functions</button>
        <button class="test-btn" onclick="inspectProducts()">Inspect Products</button>
        <div id="debug-log"></div>
    </div>

    <script>
        let debugLog = [];
        
        function addDebugLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push({ timestamp, message, type });
            updateDebugDisplay();
        }
        
        function updateDebugDisplay() {
            const logDiv = document.getElementById('debug-log');
            logDiv.innerHTML = debugLog.slice(-20).map(log => 
                `<div class="debug-log debug-${log.type}">[${log.timestamp}] ${log.message}</div>`
            ).join('');
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearDebugLog() {
            debugLog = [];
            updateDebugDisplay();
        }
        
        function testFunctions() {
            addDebugLog('Testing global functions...', 'info');
            
            const functions = ['changeMainImage', 'selectVariant', 'switchTab', 'cycleMainImage', 'showCategory'];
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addDebugLog(`✓ ${funcName} is available`, 'success');
                } else {
                    addDebugLog(`✗ ${funcName} is NOT available`, 'error');
                }
            });
        }
        
        function inspectProducts() {
            addDebugLog('Inspecting product cards...', 'info');
            
            const productCards = document.querySelectorAll('.product-card');
            addDebugLog(`Found ${productCards.length} product cards`, 'info');
            
            productCards.forEach((card, index) => {
                const productId = card.getAttribute('data-product-id');
                const category = card.getAttribute('data-category');
                const mainImg = card.querySelector('[id^="main-img-"]');
                const variants = card.querySelectorAll('.variant-option');
                const thumbnails = card.querySelectorAll('.thumbnail');
                
                addDebugLog(`Card ${index}: ID="${productId}", Category="${category}", MainImg=${!!mainImg}, Variants=${variants.length}, Thumbnails=${thumbnails.length}`, 'info');
                
                if (!productId || productId === '') {
                    addDebugLog(`⚠ Card ${index} has empty product ID!`, 'error');
                }
            });
        }
        
        // Override console methods to capture logs
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        console.log = function(...args) {
            addDebugLog(args.join(' '), 'info');
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addDebugLog(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addDebugLog(args.join(' '), 'warning');
            originalConsoleWarn.apply(console, args);
        };
        
        // Monitor clicks on the page
        document.addEventListener('click', function(e) {
            if (e.target.closest('.debug-panel')) return; // Ignore debug panel clicks
            
            const target = e.target;
            const classList = Array.from(target.classList).join('.');
            const id = target.id;
            const onclick = target.getAttribute('onclick');
            
            addDebugLog(`Click: ${target.tagName}${id ? '#' + id : ''}${classList ? '.' + classList : ''} ${onclick ? 'onclick="' + onclick + '"' : ''}`, 'info');
        });
        
        // Open products page in iframe or new window
        window.addEventListener('load', function() {
            addDebugLog('Debug page loaded', 'success');
            addDebugLog('Open the products page in another tab and watch this debug console', 'info');
            
            // Try to open products page
            setTimeout(() => {
                window.open('http://localhost:8000/pages/products.html', '_blank');
            }, 1000);
        });
    </script>
</body>
</html>
