:root {
    /* Red Color Scheme */
    --primary-red: #dc2626;
    --secondary-red: #ef4444;
    --light-red: #fef2f2;
    --dark-red: #991b1b;
    --accent-red: #f87171;
    
    /* Neutral Colors */
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --text-muted: #9ca3af;
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Spacing */
    --container-max-width: 1400px;
    --section-padding: 5rem 0;
    --card-padding: 2rem;
    
    /* Border Radius */
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-red: 0 10px 25px rgba(220, 38, 38, 0.3);
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background: linear-gradient(135deg, var(--light-red) 0%, var(--white) 50%, var(--gray-50) 100%);
    min-height: 100vh;
    transition: all var(--transition-normal);
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 24px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 1.875rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-light);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    transition: left var(--transition-normal);
    z-index: -1;
}

.btn:hover::before {
    left: 0;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
    color: white;
}

.btn-primary::before {
    background: linear-gradient(135deg, var(--dark-red), var(--primary-red));
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-red);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-red);
    border: 2px solid var(--primary-red);
}

.btn-secondary::before {
    background: var(--primary-red);
}

.btn-secondary:hover {
    color: white;
    transform: translateY(-2px);
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(220, 38, 38, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-md);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary-red);
    text-decoration: none;
    transition: all var(--transition-normal);
}

.logo:hover {
    transform: scale(1.05);
}

.logo i {
    font-size: 2.5rem;
    background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu ul {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    font-size: 1rem;
    transition: all var(--transition-normal);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    position: relative;
    overflow: hidden;
}

.nav-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
    transition: left var(--transition-normal);
    z-index: -1;
}

.nav-menu a:hover::before {
    left: 0;
}

.nav-menu a:hover {
    color: white;
    transform: translateY(-2px);
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.lang-btn, .theme-btn {
    background: var(--gray-100);
    border: none;
    padding: 0.75rem;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    color: var(--text-dark);
    font-weight: 500;
}

.lang-btn:hover, .theme-btn:hover {
    background: var(--primary-red);
    color: white;
    transform: translateY(-2px);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-dark);
    cursor: pointer;
}

/* Hero Section */
.hero {
    position: relative;
    padding: 8rem 0;
    overflow: hidden;
    color: white;
    text-align: center;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-red) 0%, var(--dark-red) 100%);
    z-index: -2;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    z-index: -1;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero h1 {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 2px 4px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.02em;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 3rem;
    opacity: 0.95;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    color: rgba(255, 255, 255, 0.9);
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 4rem;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    display: block;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Section Styles */
.section-title {
    font-size: 3rem;
    font-weight: 800;
    text-align: center;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
}

.section-subtitle {
    text-align: center;
    font-size: 1.25rem;
    color: var(--text-light);
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Featured Categories */
.featured-categories {
    padding: var(--section-padding);
    background: white;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.category-card {
    background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);
    padding: var(--card-padding);
    border-radius: var(--border-radius-xl);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.category-card:hover::before {
    transform: scaleX(1);
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--accent-red);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--light-red), rgba(254, 242, 242, 0.5));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all var(--transition-normal);
}

.category-card:hover .category-icon {
    background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
    transform: scale(1.1);
}

.category-icon i {
    font-size: 2rem;
    color: var(--primary-red);
    transition: all var(--transition-normal);
}

.category-card:hover .category-icon i {
    color: white;
}

.category-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.category-card p {
    color: var(--text-light);
    margin-bottom: 1rem;
}

.product-count {
    display: inline-block;
    background: var(--primary-red);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Features Section */
.features {
    padding: var(--section-padding);
    background: var(--gray-50);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-item {
    background: white;
    padding: var(--card-padding);
    border-radius: var(--border-radius-xl);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.feature-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all var(--transition-normal);
}

.feature-item:hover .feature-icon {
    transform: scale(1.1);
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-item h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.feature-item p {
    color: var(--text-light);
}

/* CTA Section */
.cta {
    padding: var(--section-padding);
    background: linear-gradient(135deg, var(--primary-red) 0%, var(--dark-red) 100%);
    color: white;
    text-align: center;
}

.cta h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: white;
}

.cta p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    color: rgba(255, 255, 255, 0.9);
}

.cta .btn-primary {
    background: white;
    color: var(--primary-red);
}

.cta .btn-primary::before {
    background: var(--gray-100);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
    color: white;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3 {
    color: var(--accent-red);
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.footer-section p,
.footer-section a {
    color: #d1d5db;
    text-decoration: none;
    margin-bottom: 0.75rem;
    display: block;
    transition: color var(--transition-normal);
}

.footer-section a:hover {
    color: var(--accent-red);
}

.footer-section ul {
    list-style: none;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--gray-700);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
}

.social-links a:hover {
    background: var(--primary-red);
    transform: translateY(-2px);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contact-info i {
    color: var(--accent-red);
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid var(--gray-700);
    padding-top: 2rem;
    text-align: center;
    color: var(--gray-400);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero h1 {
        font-size: 3rem;
    }
    
    .section-title {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }
    
    .header-content {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .nav-menu {
        display: none;
        width: 100%;
        order: 3;
    }
    
    .nav-menu.active {
        display: block;
    }
    
    .nav-menu ul {
        flex-direction: column;
        gap: 0.5rem;
        padding: 1rem 0;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .hero {
        padding: 6rem 0;
    }
    
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-stats {
        flex-wrap: wrap;
        gap: 2rem;
    }
    
    .categories-grid,
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .cta h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(220, 38, 38, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-red);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Scroll Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }

.hidden { display: none; }
.visible { display: block; }

