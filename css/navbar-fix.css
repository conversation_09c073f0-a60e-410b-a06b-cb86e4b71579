/* Simple and Reliable Navigation Styles */
.header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(220, 38, 38, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    max-width: 1400px;
    margin: 0 auto;
    padding-left: 24px;
    padding-right: 24px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--primary-red);
    text-decoration: none;
    transition: all 0.3s ease;
}

.logo:hover {
    transform: scale(1.02);
}

.nav-menu {
    display: flex;
}

.nav-menu ul {
    display: flex;
    list-style: none;
    gap: 0;
    align-items: center;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    margin: 0;
}

.nav-menu a {
    display: block;
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    font-size: 1rem;
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0 0.25rem;
}

.nav-menu a:hover,
.nav-menu a.active {
    background: var(--primary-red);
    color: white;
    transform: translateY(-1px);
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.language-switcher,
.theme-toggle {
    position: relative;
}

.lang-btn, 
.theme-btn {
    background: var(--gray-100);
    border: 1px solid var(--gray-200);
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-dark);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.lang-btn:hover, 
.theme-btn:hover {
    background: var(--primary-red);
    color: white;
    border-color: var(--primary-red);
    transform: translateY(-1px);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-dark);
    cursor: pointer;
    padding: 0.5rem;
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid var(--gray-200);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .nav-menu.active {
        display: block;
    }

    .nav-menu ul {
        flex-direction: column;
        gap: 0;
        padding: 1rem 0;
    }

    .nav-menu a {
        padding: 1rem 2rem;
        margin: 0;
        border-radius: 0;
        border-bottom: 1px solid var(--gray-100);
    }

    .mobile-menu-toggle {
        display: block;
    }

    .header-controls {
        gap: 0.5rem;
    }

    .lang-btn, 
    .theme-btn {
        padding: 0.5rem;
        font-size: 0.8rem;
    }
}

/* Ensure body has top padding for fixed header */
body {
    padding-top: 80px;
}

@media (max-width: 768px) {
    body {
        padding-top: 70px;
    }
}

