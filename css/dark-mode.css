/* Dark Mode Styles */
[data-theme="dark"] {
    /* Dark Mode Color Overrides */
    --text-dark: #f9fafb;
    --text-light: #d1d5db;
    --text-muted: #9ca3af;
    --white: #1f2937;
    --gray-50: #111827;
    --gray-100: #1f2937;
    --gray-200: #374151;
    --gray-300: #4b5563;
    --gray-400: #6b7280;
    --gray-500: #9ca3af;
    --gray-600: #d1d5db;
    --gray-700: #e5e7eb;
    --gray-800: #f3f4f6;
    --gray-900: #f9fafb;
    
    /* Dark mode specific colors */
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --border-color: #374151;
}

[data-theme="dark"] body {
    background: linear-gradient(135deg, #111827 0%, #1f2937 50%, #374151 100%);
    color: var(--text-dark);
}

[data-theme="dark"] .header {
    background: rgba(31, 41, 55, 0.95);
    border-bottom: 1px solid rgba(220, 38, 38, 0.2);
}

[data-theme="dark"] .nav-menu a {
    color: var(--text-dark);
}

[data-theme="dark"] .featured-categories {
    background: var(--bg-secondary);
}

[data-theme="dark"] .category-card {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-color: var(--border-color);
}

[data-theme="dark"] .category-card:hover {
    border-color: var(--accent-red);
}

[data-theme="dark"] .features {
    background: var(--bg-primary);
}

[data-theme="dark"] .feature-item {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .lang-btn,
[data-theme="dark"] .theme-btn {
    background: var(--bg-tertiary);
    color: var(--text-dark);
}

[data-theme="dark"] .lang-btn:hover,
[data-theme="dark"] .theme-btn:hover {
    background: var(--primary-red);
    color: white;
}

[data-theme="dark"] .mobile-menu-toggle {
    color: var(--text-dark);
}

/* Dark mode transitions */
[data-theme="dark"] * {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Products Page Dark Mode */
[data-theme="dark"] .products-container {
    background: var(--bg-primary);
}

[data-theme="dark"] .sidebar {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .sidebar-category {
    color: var(--text-light);
    border-color: var(--border-color);
}

[data-theme="dark"] .sidebar-category:hover,
[data-theme="dark"] .sidebar-category.active {
    background: var(--primary-red);
    color: white;
}

[data-theme="dark"] .products-grid {
    background: var(--bg-secondary);
}

[data-theme="dark"] .product-card {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-color: var(--border-color);
}

[data-theme="dark"] .product-card:hover {
    border-color: var(--accent-red);
}

[data-theme="dark"] .product-benefits {
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
}

[data-theme="dark"] .usage-instructions {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, rgba(75, 85, 99, 0.5) 100%);
}

[data-theme="dark"] .caution-storage {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
}

/* About Page Dark Mode */
[data-theme="dark"] .about-hero {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

[data-theme="dark"] .about-content {
    background: var(--bg-secondary);
}

[data-theme="dark"] .timeline-item {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .team-member {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

/* Contact Page Dark Mode */
[data-theme="dark"] .contact-hero {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

[data-theme="dark"] .contact-form {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group textarea,
[data-theme="dark"] .form-group select {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-dark);
}

[data-theme="dark"] .form-group input:focus,
[data-theme="dark"] .form-group textarea:focus,
[data-theme="dark"] .form-group select:focus {
    border-color: var(--primary-red);
    background: var(--bg-secondary);
}

[data-theme="dark"] .contact-info-card {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

/* Theme Toggle Icon */
[data-theme="dark"] #theme-icon::before {
    content: "\f185"; /* fa-sun */
}

[data-theme="light"] #theme-icon::before {
    content: "\f186"; /* fa-moon */
}

/* Smooth theme transition */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

