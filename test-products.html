<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Products Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .test-button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
    </style>
</head>
<body>
    <h1>Products Page Functionality Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Check if functions are available</h2>
        <button class="test-button" onclick="testFunctionsAvailable()">Test Functions</button>
        <div id="functions-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Open Products Page</h2>
        <button class="test-button" onclick="openProductsPage()">Open Products Page</button>
        <div id="page-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Instructions</h2>
        <div class="result">
            <h3>Manual Testing Steps:</h3>
            <ol>
                <li>Click "Open Products Page" above</li>
                <li>Try clicking on different categories in the sidebar</li>
                <li>For products with multiple thumbnails (like Dishwashing Liquid), try:
                    <ul>
                        <li>Clicking on different thumbnail images</li>
                        <li>Clicking on different size options</li>
                        <li>Clicking on the main product image to cycle through</li>
                    </ul>
                </li>
                <li>Check the browser console (F12) for any error messages</li>
                <li>Verify that images change when you interact with the product</li>
            </ol>
        </div>
    </div>

    <script>
        function testFunctionsAvailable() {
            const resultDiv = document.getElementById('functions-result');
            const functions = ['changeMainImage', 'selectVariant', 'switchTab', 'cycleMainImage'];
            let results = [];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    results.push(`✓ ${funcName} is available`);
                } else {
                    results.push(`✗ ${funcName} is NOT available`);
                }
            });
            
            resultDiv.innerHTML = results.join('<br>');
            resultDiv.className = 'result ' + (results.some(r => r.includes('NOT')) ? 'error' : 'success');
        }

        function openProductsPage() {
            const resultDiv = document.getElementById('page-result');
            try {
                window.open('http://localhost:8000/pages/products.html', '_blank');
                resultDiv.innerHTML = '✓ Products page opened in new tab';
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.innerHTML = '✗ Error opening products page: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        // Test on page load
        window.addEventListener('load', function() {
            console.log('Test page loaded');
        });
    </script>
</body>
</html>
